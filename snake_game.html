<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🐍 超级贪吃蛇 🐍</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Arial', sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            color: white;
        }
        
        .game-container {
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 20px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        
        .game-header {
            margin-bottom: 20px;
        }
        
        .score-board {
            display: flex;
            justify-content: space-around;
            margin-bottom: 15px;
            font-size: 18px;
            font-weight: bold;
        }
        
        .score-item {
            background: rgba(255, 255, 255, 0.2);
            padding: 10px 15px;
            border-radius: 10px;
            min-width: 100px;
        }
        
        canvas {
            border: 3px solid #fff;
            border-radius: 10px;
            background: #000;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
        }
        
        .controls {
            margin-top: 20px;
            font-size: 14px;
            opacity: 0.8;
        }
        
        .game-over {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            display: none;
        }
        
        .restart-btn {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            border: none;
            color: white;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            margin-top: 15px;
            transition: transform 0.2s;
        }
        
        .restart-btn:hover {
            transform: scale(1.05);
        }
        
        .power-up-info {
            font-size: 12px;
            margin-top: 10px;
            opacity: 0.7;
        }
    </style>
</head>
<body>
    <div class="game-container">
        <div class="game-header">
            <h1>🐍 超级贪吃蛇 🐍</h1>
            <div class="score-board">
                <div class="score-item">
                    <div>分数</div>
                    <div id="score">0</div>
                </div>
                <div class="score-item">
                    <div>长度</div>
                    <div id="length">1</div>
                </div>
                <div class="score-item">
                    <div>等级</div>
                    <div id="level">1</div>
                </div>
            </div>
        </div>
        
        <canvas id="gameCanvas" width="600" height="400"></canvas>
        
        <div class="controls">
            <p>🎮 使用方向键或 WASD 控制蛇的移动</p>
            <div class="power-up-info">
                🍎 普通食物 +10分 | 🍓 草莓 +20分 | ⚡ 闪电 +50分 加速 | 🌟 星星 +30分 穿墙 | 💎 钻石 +100分
            </div>
        </div>
        
        <div class="game-over" id="gameOver">
            <h2>🎮 游戏结束 🎮</h2>
            <p>最终分数: <span id="finalScore">0</span></p>
            <p>最终长度: <span id="finalLength">0</span></p>
            <button class="restart-btn" onclick="restartGame()">🔄 重新开始</button>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('gameCanvas');
        const ctx = canvas.getContext('2d');
        const gameOverDiv = document.getElementById('gameOver');

        // 游戏配置
        const GRID_SIZE = 20;
        const CANVAS_WIDTH = canvas.width;
        const CANVAS_HEIGHT = canvas.height;

        // 游戏状态
        let snake = [{x: 10, y: 10}];
        let direction = {x: 0, y: 0};
        let food = {};
        let score = 0;
        let gameRunning = false;
        let gameSpeed = 150;
        let level = 1;
        let particles = [];
        let powerUps = [];
        let snakeSpeed = 1;
        let canPassWalls = false;
        let wallPassTimer = 0;

        // 食物类型
        const FOOD_TYPES = {
            NORMAL: { emoji: '🍎', points: 10, color: '#ff4757' },
            BERRY: { emoji: '🍓', points: 20, color: '#ff3838' },
            LIGHTNING: { emoji: '⚡', points: 50, color: '#ffd700', effect: 'speed' },
            STAR: { emoji: '🌟', points: 30, color: '#ffd700', effect: 'wall' },
            DIAMOND: { emoji: '💎', points: 100, color: '#00d2d3' }
        };

        // 粒子系统
        class Particle {
            constructor(x, y, color) {
                this.x = x;
                this.y = y;
                this.vx = (Math.random() - 0.5) * 4;
                this.vy = (Math.random() - 0.5) * 4;
                this.life = 30;
                this.color = color;
                this.size = Math.random() * 3 + 1;
            }

            update() {
                this.x += this.vx;
                this.y += this.vy;
                this.life--;
                this.size *= 0.98;
            }

            draw() {
                ctx.save();
                ctx.globalAlpha = this.life / 30;
                ctx.fillStyle = this.color;
                ctx.beginPath();
                ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
                ctx.fill();
                ctx.restore();
            }
        }

        // 初始化游戏
        function initGame() {
            snake = [{x: 10, y: 10}];
            direction = {x: 0, y: 0};
            score = 0;
            level = 1;
            gameSpeed = 150;
            snakeSpeed = 1;
            canPassWalls = false;
            wallPassTimer = 0;
            particles = [];
            powerUps = [];
            generateFood();
            updateUI();
        }

        // 生成食物
        function generateFood() {
            const types = Object.keys(FOOD_TYPES);
            let foodType;
            
            // 根据分数调整特殊食物出现概率
            const rand = Math.random();
            if (score > 500 && rand < 0.1) {
                foodType = 'DIAMOND';
            } else if (score > 200 && rand < 0.15) {
                foodType = 'LIGHTNING';
            } else if (score > 100 && rand < 0.2) {
                foodType = 'STAR';
            } else if (rand < 0.3) {
                foodType = 'BERRY';
            } else {
                foodType = 'NORMAL';
            }

            food = {
                x: Math.floor(Math.random() * (CANVAS_WIDTH / GRID_SIZE)),
                y: Math.floor(Math.random() * (CANVAS_HEIGHT / GRID_SIZE)),
                type: foodType,
                ...FOOD_TYPES[foodType]
            };

            // 确保食物不在蛇身上
            for (let segment of snake) {
                if (segment.x === food.x && segment.y === food.y) {
                    generateFood();
                    return;
                }
            }
        }

        // 移动蛇
        function moveSnake() {
            if (direction.x === 0 && direction.y === 0) return;

            const head = {x: snake[0].x + direction.x, y: snake[0].y + direction.y};

            // 墙壁碰撞检测
            if (!canPassWalls) {
                if (head.x < 0 || head.x >= CANVAS_WIDTH / GRID_SIZE || 
                    head.y < 0 || head.y >= CANVAS_HEIGHT / GRID_SIZE) {
                    gameOver();
                    return;
                }
            } else {
                // 穿墙效果
                if (head.x < 0) head.x = CANVAS_WIDTH / GRID_SIZE - 1;
                if (head.x >= CANVAS_WIDTH / GRID_SIZE) head.x = 0;
                if (head.y < 0) head.y = CANVAS_HEIGHT / GRID_SIZE - 1;
                if (head.y >= CANVAS_HEIGHT / GRID_SIZE) head.y = 0;
            }

            // 自身碰撞检测
            for (let segment of snake) {
                if (head.x === segment.x && head.y === segment.y) {
                    gameOver();
                    return;
                }
            }

            snake.unshift(head);

            // 检查是否吃到食物
            if (head.x === food.x && head.y === food.y) {
                eatFood();
            } else {
                snake.pop();
            }
        }

        // 吃食物
        function eatFood() {
            score += food.points;
            
            // 创建粒子效果
            for (let i = 0; i < 10; i++) {
                particles.push(new Particle(
                    food.x * GRID_SIZE + GRID_SIZE / 2,
                    food.y * GRID_SIZE + GRID_SIZE / 2,
                    food.color
                ));
            }

            // 处理特殊效果
            if (food.effect === 'speed') {
                snakeSpeed = 2;
                setTimeout(() => snakeSpeed = 1, 5000);
            } else if (food.effect === 'wall') {
                canPassWalls = true;
                wallPassTimer = 300; // 5秒
            }

            // 升级检查
            const newLevel = Math.floor(score / 200) + 1;
            if (newLevel > level) {
                level = newLevel;
                gameSpeed = Math.max(80, gameSpeed - 10);
            }

            generateFood();
            updateUI();
        }

        // 更新UI
        function updateUI() {
            document.getElementById('score').textContent = score;
            document.getElementById('length').textContent = snake.length;
            document.getElementById('level').textContent = level;
        }

        // 绘制游戏
        function draw() {
            // 清空画布
            ctx.fillStyle = '#000';
            ctx.fillRect(0, 0, CANVAS_WIDTH, CANVAS_HEIGHT);

            // 绘制网格
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 0.5;
            for (let i = 0; i <= CANVAS_WIDTH; i += GRID_SIZE) {
                ctx.beginPath();
                ctx.moveTo(i, 0);
                ctx.lineTo(i, CANVAS_HEIGHT);
                ctx.stroke();
            }
            for (let i = 0; i <= CANVAS_HEIGHT; i += GRID_SIZE) {
                ctx.beginPath();
                ctx.moveTo(0, i);
                ctx.lineTo(CANVAS_WIDTH, i);
                ctx.stroke();
            }

            // 绘制蛇
            snake.forEach((segment, index) => {
                const gradient = ctx.createRadialGradient(
                    segment.x * GRID_SIZE + GRID_SIZE / 2,
                    segment.y * GRID_SIZE + GRID_SIZE / 2,
                    0,
                    segment.x * GRID_SIZE + GRID_SIZE / 2,
                    segment.y * GRID_SIZE + GRID_SIZE / 2,
                    GRID_SIZE / 2
                );

                if (index === 0) {
                    // 蛇头
                    gradient.addColorStop(0, '#00ff00');
                    gradient.addColorStop(1, '#008800');
                } else {
                    // 蛇身
                    const alpha = 1 - (index / snake.length) * 0.5;
                    gradient.addColorStop(0, `rgba(0, 255, 0, ${alpha})`);
                    gradient.addColorStop(1, `rgba(0, 136, 0, ${alpha})`);
                }

                ctx.fillStyle = gradient;
                ctx.fillRect(
                    segment.x * GRID_SIZE + 1,
                    segment.y * GRID_SIZE + 1,
                    GRID_SIZE - 2,
                    GRID_SIZE - 2
                );

                // 蛇头眼睛
                if (index === 0) {
                    ctx.fillStyle = '#fff';
                    ctx.fillRect(segment.x * GRID_SIZE + 5, segment.y * GRID_SIZE + 5, 3, 3);
                    ctx.fillRect(segment.x * GRID_SIZE + 12, segment.y * GRID_SIZE + 5, 3, 3);
                }
            });

            // 绘制食物
            ctx.font = `${GRID_SIZE - 2}px Arial`;
            ctx.textAlign = 'center';
            ctx.fillText(
                food.emoji,
                food.x * GRID_SIZE + GRID_SIZE / 2,
                food.y * GRID_SIZE + GRID_SIZE - 2
            );

            // 绘制粒子
            particles.forEach(particle => particle.draw());

            // 绘制特效提示
            if (canPassWalls && wallPassTimer > 0) {
                ctx.fillStyle = 'rgba(255, 215, 0, 0.3)';
                ctx.fillRect(0, 0, CANVAS_WIDTH, CANVAS_HEIGHT);
                ctx.fillStyle = '#ffd700';
                ctx.font = '20px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('🌟 穿墙模式 🌟', CANVAS_WIDTH / 2, 30);
            }

            if (snakeSpeed > 1) {
                ctx.fillStyle = '#ffd700';
                ctx.font = '16px Arial';
                ctx.textAlign = 'right';
                ctx.fillText('⚡ 加速中', CANVAS_WIDTH - 10, 25);
            }
        }

        // 更新游戏状态
        function update() {
            if (!gameRunning) return;

            moveSnake();

            // 更新粒子
            particles = particles.filter(particle => {
                particle.update();
                return particle.life > 0;
            });

            // 更新穿墙计时器
            if (wallPassTimer > 0) {
                wallPassTimer--;
                if (wallPassTimer === 0) {
                    canPassWalls = false;
                }
            }

            draw();
        }

        // 游戏结束
        function gameOver() {
            gameRunning = false;
            document.getElementById('finalScore').textContent = score;
            document.getElementById('finalLength').textContent = snake.length;
            gameOverDiv.style.display = 'block';
        }

        // 重新开始游戏
        function restartGame() {
            gameOverDiv.style.display = 'none';
            initGame();
            startGame();
        }

        // 开始游戏
        function startGame() {
            gameRunning = true;
            gameLoop();
        }

        // 游戏主循环
        function gameLoop() {
            if (gameRunning) {
                update();
                setTimeout(() => gameLoop(), gameSpeed / snakeSpeed);
            }
        }

        // 键盘控制
        document.addEventListener('keydown', (e) => {
            if (!gameRunning && e.code !== 'Space') return;

            switch(e.code) {
                case 'ArrowUp':
                case 'KeyW':
                    if (direction.y !== 1) direction = {x: 0, y: -1};
                    break;
                case 'ArrowDown':
                case 'KeyS':
                    if (direction.y !== -1) direction = {x: 0, y: 1};
                    break;
                case 'ArrowLeft':
                case 'KeyA':
                    if (direction.x !== 1) direction = {x: -1, y: 0};
                    break;
                case 'ArrowRight':
                case 'KeyD':
                    if (direction.x !== -1) direction = {x: 1, y: 0};
                    break;
                case 'Space':
                    if (!gameRunning) {
                        restartGame();
                    }
                    break;
            }
        });

        // 初始化并开始游戏
        initGame();
        draw();
        
        // 点击开始游戏
        canvas.addEventListener('click', () => {
            if (!gameRunning && direction.x === 0 && direction.y === 0) {
                direction = {x: 1, y: 0};
                startGame();
            }
        });
    </script>
</body>
</html>